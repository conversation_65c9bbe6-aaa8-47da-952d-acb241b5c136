#!/usr/bin/env python3
"""
Validation script for the product match consolidation.

This script validates that:
1. All unique products from original files exist in the unified version
2. All match relationships are preserved
3. No products are duplicated or lost
4. Counts match expectations
"""

import json
import os
from collections import defaultdict, Counter
from typing import Dict, Set, List, Any, Tu<PERSON>

def load_json_file(filepath: str) -> List[Dict[str, Any]]:
    """Load and parse a JSON file."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: File {filepath} not found")
        return []
    except json.JSONDecodeError as e:
        print(f"Error parsing {filepath}: {e}")
        return []

def extract_products_from_original_files(matches_dir: str = "matches") -> Tuple[Dict[str, str], Dict[str, Set[str]]]:
    """
    Extract all products and their relationships from original files.

    Returns:
        Tuple of (all_products, product_relationships)
        - all_products: {product_id: product_name}
        - product_relationships: {product_id: set_of_related_product_ids}
    """
    sellers = ['chef', 'sham', 'sysco', 'usfood', 'greco', 'depot', 'perf']
    all_products = {}
    product_relationships = defaultdict(set)

    print("Analyzing original files...")

    for seller in sellers:
        filepath = os.path.join(matches_dir, f"{seller}.json")
        data = load_json_file(filepath)

        if not data:
            continue

        product_key = f"{seller}Product"
        seller_products = 0

        for entry in data:
            # Get the main product
            main_product = entry.get(product_key, {})
            if not main_product or len(main_product) != 1:
                continue

            main_product_id = list(main_product.keys())[0]
            main_product_name = list(main_product.values())[0]

            # Store product
            all_products[main_product_id] = main_product_name
            seller_products += 1

            # Get matches
            matches = entry.get('matches', {})

            # Handle case where matches might be a list or other type
            if not isinstance(matches, dict):
                matches = {}

            # Build relationships
            related_ids = {main_product_id}
            for match_id, match_name in matches.items():
                related_ids.add(match_id)
                all_products[match_id] = match_name

            # Update relationships
            for product_id in related_ids:
                product_relationships[product_id].update(related_ids)

        print(f"  {seller}: {seller_products} products, {len(data)} entries")

    return all_products, product_relationships

def extract_products_from_unified_file(filepath: str = "unified_matches.json") -> Tuple[Dict[str, str], Dict[str, Set[str]]]:
    """
    Extract all products and relationships from unified file.

    Returns:
        Tuple of (all_products, product_relationships)
    """
    data = load_json_file(filepath)
    if not data:
        return {}, defaultdict(set)

    all_products = {}
    product_relationships = defaultdict(set)

    print(f"Analyzing unified file: {len(data)} groups")

    for group in data:
        if not isinstance(group, dict):
            continue

        # Extract products from this group
        group_products = set(group.keys())

        # Store all products
        for product_id, product_name in group.items():
            all_products[product_id] = product_name

        # Build relationships (each product related to all others in group)
        for product_id in group_products:
            product_relationships[product_id].update(group_products)

    return all_products, product_relationships

def validate_product_preservation(original_products: Dict[str, str], unified_products: Dict[str, str]) -> bool:
    """Validate that all original products exist in unified version."""
    print("\n=== Product Preservation Validation ===")

    original_ids = set(original_products.keys())
    unified_ids = set(unified_products.keys())

    missing_in_unified = original_ids - unified_ids
    extra_in_unified = unified_ids - original_ids

    print(f"Original products: {len(original_ids)}")
    print(f"Unified products: {len(unified_ids)}")

    if missing_in_unified:
        print(f"❌ Missing {len(missing_in_unified)} products in unified file:")
        for product_id in list(missing_in_unified)[:10]:
            print(f"  {product_id}: {original_products[product_id][:60]}...")
        if len(missing_in_unified) > 10:
            print(f"  ... and {len(missing_in_unified) - 10} more")

    if extra_in_unified:
        print(f"⚠️  Extra {len(extra_in_unified)} products in unified file:")
        for product_id in list(extra_in_unified)[:10]:
            print(f"  {product_id}: {unified_products[product_id][:60]}...")
        if len(extra_in_unified) > 10:
            print(f"  ... and {len(extra_in_unified) - 10} more")

    # Check for name mismatches
    name_mismatches = 0
    for product_id in original_ids & unified_ids:
        if original_products[product_id] != unified_products[product_id]:
            name_mismatches += 1
            if name_mismatches <= 5:
                print(f"⚠️  Name mismatch for {product_id}:")
                print(f"    Original: {original_products[product_id]}")
                print(f"    Unified:  {unified_products[product_id]}")

    if name_mismatches > 5:
        print(f"  ... and {name_mismatches - 5} more name mismatches")

    success = len(missing_in_unified) == 0 and name_mismatches == 0
    if success:
        print("✅ All products preserved correctly")

    return success

def validate_relationships(original_relationships: Dict[str, Set[str]],
                         unified_relationships: Dict[str, Set[str]]) -> bool:
    """Validate that all match relationships are preserved."""
    print("\n=== Relationship Preservation Validation ===")

    # Check that all original relationships exist in unified
    missing_relationships = 0
    extra_relationships = 0

    all_product_ids = set(original_relationships.keys()) | set(unified_relationships.keys())

    for product_id in all_product_ids:
        original_related = original_relationships.get(product_id, set())
        unified_related = unified_relationships.get(product_id, set())

        missing = original_related - unified_related
        extra = unified_related - original_related

        if missing:
            missing_relationships += len(missing)
            if missing_relationships <= 10:
                print(f"❌ Missing relationships for {product_id}: {missing}")

        if extra:
            extra_relationships += len(extra)
            if extra_relationships <= 10:
                print(f"⚠️  Extra relationships for {product_id}: {extra}")

    print(f"Missing relationships: {missing_relationships}")
    print(f"Extra relationships: {extra_relationships}")

    success = missing_relationships == 0
    if success:
        print("✅ All relationships preserved correctly")

    return success

def analyze_seller_distribution(unified_products: Dict[str, str]) -> None:
    """Analyze distribution of products by seller."""
    print("\n=== Seller Distribution Analysis ===")

    seller_counts = Counter()

    for product_id in unified_products:
        # Extract seller from product ID (everything before first underscore)
        if '_' in product_id:
            seller = product_id.split('_')[0]
        elif '#' in product_id:
            seller = product_id.split('#')[0].replace('usfood_', 'usfood')
        else:
            seller = 'unknown'

        seller_counts[seller] += 1

    print("Products by seller:")
    for seller, count in seller_counts.most_common():
        print(f"  {seller}: {count}")

def main():
    """Main validation function."""
    print("=== Product Match Consolidation Validation ===")

    # Check if files exist
    if not os.path.exists("matches"):
        print("Error: 'matches' directory not found")
        return

    if not os.path.exists("unified_matches.json"):
        print("Error: 'unified_matches.json' not found. Run consolidate_matches.py first.")
        return

    # Extract data from original files
    original_products, original_relationships = extract_products_from_original_files()

    # Extract data from unified file
    unified_products, unified_relationships = extract_products_from_unified_file()

    # Run validations
    products_valid = validate_product_preservation(original_products, unified_products)
    relationships_valid = validate_relationships(original_relationships, unified_relationships)

    # Additional analysis
    analyze_seller_distribution(unified_products)

    # Final result
    print("\n=== Validation Summary ===")
    if products_valid and relationships_valid:
        print("✅ Consolidation validation PASSED")
    else:
        print("❌ Consolidation validation FAILED")
        if not products_valid:
            print("  - Product preservation issues found")
        if not relationships_valid:
            print("  - Relationship preservation issues found")

if __name__ == "__main__":
    main()
