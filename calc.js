const fs = require('fs');
const data = JSON.parse(fs.readFileSync('sysco_db.json', 'utf8'));

// 1. Objects with unusual caseprice suffixes
const casepriceSuffixCounts = {};
const casepriceSuffixSet = new Set();
let unusualCasepriceCount = 0;

data.forEach(obj => {
  if (obj.caseprice) {
    const match = obj.caseprice.match(/([A-Za-z]{2})\s*$/);
    if (match) {
      const suffix = match[1].toLowerCase();
      if (suffix !== 'ea' && suffix !== 'cs') {
        unusualCasepriceCount++;
        casepriceSuffixSet.add(match[1]);
        casepriceSuffixCounts[match[1]] = (casepriceSuffixCounts[match[1]] || 0) + 1;
      }
    }
  }
});

console.log(`1. Number of objects with unusual caseprice suffix: ${unusualCasepriceCount}`);
console.log('Unique suffixes (other than "ea" or "cs"):', Array.from(casepriceSuffixSet));
console.log('Suffix counts:', casepriceSuffixCounts);

// 2. Clean packsize for objects with priceunittype
const cleanedPacksizeCounts = {};
// 3. Count each unique unittype for objects with priceunittype
const unittypeCounts = {};

data.forEach(obj => {
  if (obj.priceunittype) {
    // Count each unique unittype
    if (obj.unittype) {
      unittypeCounts[obj.unittype] = (unittypeCounts[obj.unittype] || 0) + 1;
    }
    // Clean packsize
    if (obj.packsize) {
      const cleaned = obj.packsize.replace(/[0-9Xx\/\-]/g, '').trim();
      if (cleaned) {
        cleanedPacksizeCounts[cleaned] = (cleanedPacksizeCounts[cleaned] || 0) + 1;
      }
    }
  }
});

console.log('\n2. Unique cleaned packsize values and their counts (for objects with priceunittype):');
Object.entries(cleanedPacksizeCounts).forEach(([str, count]) => {
  console.log(`"${str}": ${count}`);
});

console.log('\n3. Unique unittype values and their counts (for objects with priceunittype):');
Object.entries(unittypeCounts).forEach(([unit, count]) => {
  console.log(`"${unit}": ${count}`);
});
