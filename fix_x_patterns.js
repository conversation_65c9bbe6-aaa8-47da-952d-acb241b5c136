const fs = require('fs');

// Read sysco_db.json file
const syscoData = JSON.parse(fs.readFileSync('sysco_db.json', 'utf8'));

console.log(`Total objects in sysco_db.json: ${syscoData.length}`);

// Function to parse packsize correctly, ignoring XxY patterns
function parsePacksizeCorrectly(packsize) {
    if (!packsize || typeof packsize !== 'string') {
        return { quantity: null, unittype: null, error: 'Invalid packsize' };
    }

    // Clean the packsize string
    let cleanPacksize = packsize.trim();

    // Handle patterns ending with " (inches)
    if (cleanPacksize.endsWith('"')) {
        // Check if it has space-separated patterns like "1400/1 7/8""
        const parts = cleanPacksize.split(' ');
        if (parts.length > 1) {
            return { quantity: null, unittype: 'inch', error: 'Complex inch pattern - manual processing needed' };
        }

        // Simple inch pattern like "8.75""
        const numberPart = cleanPacksize.slice(0, -1);

        // Remove XxY patterns before processing
        const cleanedNumberPart = removeXPatterns(numberPart);
        const numbers = cleanedNumberPart.split('/').map(n => parseFloat(n.startsWith('.') ? '0' + n : n));

        if (numbers.some(isNaN)) {
            return { quantity: null, unittype: 'inch', error: 'Invalid numbers in inch pattern' };
        }

        const quantity = numbers.reduce((acc, num) => acc * num, 1);
        return { quantity, unittype: 'inch', error: null };
    }

    // Extract unit type (last part that's not a number)
    let unittype = null;
    let numberPart = cleanPacksize;

    // Handle # at the end (means pounds)
    if (cleanPacksize.endsWith('#')) {
        unittype = 'lb';
        numberPart = cleanPacksize.slice(0, -1);
    } else {
        // Extract unit from the end
        const match = cleanPacksize.match(/([a-zA-Z]+)$/);
        if (match) {
            unittype = match[1].toLowerCase();
            // Convert common variations
            if (unittype === 'lbs') unittype = 'lb';
            if (unittype === 'foz') unittype = 'FOZ';
            if (unittype === 'oz') unittype = 'OZ';
            if (unittype === 'lb') unittype = 'LB';
            if (unittype === 'gal') unittype = 'GAL';
            if (unittype === 'lt') unittype = 'LT';
            if (unittype === 'cs') unittype = 'CS';
            if (unittype === 'ea') unittype = 'EA';
            if (unittype === 'ct') unittype = 'CT';
            if (unittype === 'each') unittype = 'EACH';
            if (unittype === 'indiv') unittype = 'INDIV';
            if (unittype === 'z') unittype = 'Z';

            numberPart = cleanPacksize.replace(new RegExp(match[1] + '$'), '');
        }
    }

    if (!unittype) {
        return { quantity: null, unittype: null, error: 'No unit type found' };
    }

    // Remove XxY patterns from numberPart before processing
    const cleanedNumberPart = removeXPatterns(numberPart);

    // Handle patterns like "6/EA" - single number with unit
    if (cleanedNumberPart.includes('/') && cleanedNumberPart.split('/').length === 2) {
        const parts = cleanedNumberPart.split('/');
        if (parts[1] === '' || parts[1] === unittype.toLowerCase()) {
            // Pattern like "6/EA" or "6/"
            const quantity = parseFloat(parts[0].startsWith('.') ? '0' + parts[0] : parts[0]);
            if (isNaN(quantity)) {
                return { quantity: null, unittype, error: 'Invalid number' };
            }
            return { quantity, unittype, error: null };
        }
    }

    // Parse numbers with / (multiply) or - (average)
    let quantity = null;

    // Remove any spaces and handle patterns without spaces
    const finalNumberPart = cleanedNumberPart.replace(/\s+/g, '');

    if (finalNumberPart.includes('/')) {
        // Split by / and multiply
        const numbers = finalNumberPart.split('/').map(n => {
            if (n === '') return null;
            // Handle numbers starting with . like .88
            const num = parseFloat(n.startsWith('.') ? '0' + n : n);
            return isNaN(num) ? null : num;
        }).filter(n => n !== null);

        if (numbers.length === 0) {
            return { quantity: null, unittype, error: 'No valid numbers found' };
        }

        quantity = numbers.reduce((acc, num) => acc * num, 1);
    } else if (finalNumberPart.includes('-')) {
        // Split by - and average
        const numbers = finalNumberPart.split('-').map(n => {
            const num = parseFloat(n.startsWith('.') ? '0' + n : n);
            return isNaN(num) ? null : num;
        }).filter(n => n !== null);

        if (numbers.length === 0) {
            return { quantity: null, unittype, error: 'No valid numbers found' };
        }

        quantity = numbers.reduce((acc, num) => acc + num, 0) / numbers.length;
    } else {
        // Single number
        const num = parseFloat(finalNumberPart.startsWith('.') ? '0' + finalNumberPart : finalNumberPart);
        if (isNaN(num)) {
            return { quantity: null, unittype, error: 'Invalid single number' };
        }
        quantity = num;
    }

    return { quantity, unittype, error: null };
}

// Function to remove XxY patterns from a string
function removeXPatterns(str) {
    // Remove patterns like "12X15", "5x4x6", "12x15x20", etc.
    // This regex matches: number + x/X + number (and potentially more x/X + number combinations)
    return str.replace(/\d+[xX]\d+([xX]\d+)*/g, '');
}

// Unit conversion factors and functions (same as before)
const unitConversions = {
    'OZ': 1/16, 'LB': 1, 'LBS': 1, '#': 1,
    'FOZ': 1, 'FL OZ': 1, 'PT': 16, 'QT': 32, 'QUART': 32,
    'GAL': 128, 'GALLON': 128, 'LT': 33.814, 'LITER': 33.814,
};

function getUnitCategory(unit) {
    const upperUnit = unit.toUpperCase();
    if (['OZ', 'LB', 'LBS', '#'].includes(upperUnit)) {
        return { category: 'weight', baseUnit: 'LB' };
    }
    if (['FOZ', 'FL OZ', 'PT', 'QT', 'QUART', 'GAL', 'GALLON', 'LT', 'LITER'].includes(upperUnit)) {
        return { category: 'volume', baseUnit: 'FOZ' };
    }
    return { category: 'other', baseUnit: upperUnit };
}

function convertQuantity(quantity, fromUnit, toUnit) {
    const fromCategory = getUnitCategory(fromUnit);
    const toCategory = getUnitCategory(toUnit);

    if (fromCategory.category !== toCategory.category || fromCategory.category === 'other') {
        return { quantity, unit: fromUnit };
    }

    const fromUpper = fromUnit.toUpperCase();
    const toUpper = toUnit.toUpperCase();

    if (fromUpper === toUpper || (fromUpper === '#' && toUpper === 'LB') || (fromUpper === 'LB' && toUpper === '#')) {
        return { quantity, unit: toUnit };
    }

    const fromFactor = unitConversions[fromUpper];
    const toFactor = unitConversions[toUpper];

    if (fromFactor && toFactor) {
        const convertedQuantity = (quantity * fromFactor) / toFactor;
        return { quantity: convertedQuantity, unit: toUnit };
    }

    return { quantity, unit: fromUnit };
}

// Function to calculate pricing (same as before)
function calculatePricing(item, quantity, unittype) {
    const caseprice = item.caseprice;
    const unitprice = item.unitprice;

    let price = null;
    let portionprice = null;
    let pricingMethod = 'no_price';
    let priceunittype = null;

    const extractPrice = (priceStr) => {
        if (!priceStr) return { value: null, unit: null };
        const match = priceStr.match(/\$?([\d.]+)\s*([A-Z]+)$/i);
        if (match) {
            return { value: parseFloat(match[1]), unit: match[2].toUpperCase() };
        }
        return { value: null, unit: null };
    };

    const caseInfo = extractPrice(caseprice);
    const unitInfo = extractPrice(unitprice);

    let priceInfo = null;
    let priceSource = null;

    if (caseInfo.value !== null) {
        priceInfo = caseInfo;
        priceSource = 'case';
    } else if (unitInfo.value !== null) {
        priceInfo = unitInfo;
        priceSource = 'unit';
    }

    if (priceInfo && quantity) {
        if (priceInfo.unit.toUpperCase() === 'EA' || priceInfo.unit.toUpperCase() === 'CS') {
            price = priceInfo.value;
            portionprice = price / quantity;
            pricingMethod = priceSource === 'case' ? 'case_ea_cs' : 'unit_ea_cs';
        } else {
            priceunittype = priceInfo.unit;
            portionprice = priceInfo.value;

            let finalQuantity = quantity;
            let finalUnittype = unittype;

            if (unittype.toUpperCase() !== priceunittype.toUpperCase()) {
                const conversion = convertQuantity(quantity, unittype, priceunittype);
                finalQuantity = conversion.quantity;
                finalUnittype = conversion.unit;
            }

            price = portionprice * finalQuantity;
            pricingMethod = priceSource === 'case' ? 'case_portion' : 'unit_portion';

            if (finalQuantity !== quantity) {
                return {
                    price, portionprice, pricingMethod, priceunittype,
                    convertedQuantity: finalQuantity, convertedUnittype: finalUnittype
                };
            }
        }
    }

    return { price, portionprice, pricingMethod, priceunittype };
}

// Process sysco_db.json
let correctedCount = 0;
let xPatternCount = 0;

console.log('\n=== Processing sysco_db.json ===');
for (const item of syscoData) {
    // Check if packsize contains X patterns
    if (item.packsize && /\d+[xX]\d+/.test(item.packsize)) {
        xPatternCount++;

        // Re-parse the packsize correctly
        const parseResult = parsePacksizeCorrectly(item.packsize);

        // Even if parseResult has errors, we still want to correct the quantity if X patterns were removed
        const oldQuantity = item.quantity;
        const oldUnittype = item.unittype;

        // If parsing was successful, use the new values
        if (parseResult.error === null && parseResult.quantity !== null) {
            item.quantity = parseResult.quantity;
            if (parseResult.unittype) {
                item.unittype = parseResult.unittype;
            }
        } else {
            // If parsing failed but we removed X patterns, try to recalculate quantity manually
            const cleanedPacksize = removeXPatterns(item.packsize);

            // Try to extract just the first number if no unit is found
            const firstNumberMatch = cleanedPacksize.match(/^(\d+(?:\.\d+)?)/);
            if (firstNumberMatch) {
                item.quantity = parseFloat(firstNumberMatch[1]);
                console.log(`✓ Manual correction: ${item.packsize} -> quantity: ${oldQuantity} → ${item.quantity} (no unit)`);
                correctedCount++;
                continue;
            }
        }

        // Recalculate pricing if we have valid quantity
        if (item.quantity !== null && item.quantity !== undefined) {
            const pricingResult = calculatePricing(item, item.quantity, item.unittype);

            item.price = pricingResult.price;
            item.portionprice = pricingResult.portionprice;
            item.pricingMethod = pricingResult.pricingMethod;
            if (pricingResult.priceunittype) {
                item.priceunittype = pricingResult.priceunittype;
            }

            // Handle unit conversion if it occurred
            if (pricingResult.convertedQuantity !== undefined) {
                item.quantity = pricingResult.convertedQuantity;
                item.unittype = pricingResult.convertedUnittype;
            }

            if (parseResult.error === null) {
                correctedCount++;
                console.log(`✓ Corrected: ${item.packsize} -> quantity: ${oldQuantity} → ${item.quantity}`);
            }
        }
    }
}

console.log(`\nCorrection Summary:`);
console.log(`- Items with X patterns found: ${xPatternCount}`);
console.log(`- Items successfully corrected: ${correctedCount}`);

// Write the corrected data back
fs.writeFileSync('sysco_db.json', JSON.stringify(syscoData, null, 2));
console.log(`\nCorrected data written back to sysco_db.json`);
