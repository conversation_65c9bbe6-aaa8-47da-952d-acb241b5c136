#!/usr/bin/env python3
"""
Script to consolidate product matches from 7 JSON files into a single unified JSON file.

This script reads match data from chef.json, sham.json, sysco.json, usfood.json,
greco.json, depot.json, and perf.json files and creates a unified matches.json file
where each object represents a complete set of matched products across all sellers.
"""

import json
import os
from collections import defaultdict
from typing import Dict, Set, List, Any

def load_json_file(filepath: str) -> List[Dict[str, Any]]:
    """Load and parse a JSON file."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Warning: File {filepath} not found")
        return []
    except json.JSONDecodeError as e:
        print(f"Error parsing {filepath}: {e}")
        return []

def extract_seller_from_filename(filename: str) -> str:
    """Extract seller name from filename (e.g., 'chef.json' -> 'chef')."""
    return os.path.splitext(filename)[0]

def get_product_key_name(seller: str) -> str:
    """Get the expected product key name for a seller."""
    return f"{seller}Product"

def consolidate_matches(matches_dir: str = "matches") -> List[Dict[str, str]]:
    """
    Consolidate all match files into unified match groups.

    Returns:
        List of dictionaries where each dict contains all matched products
        across sellers with format: {"seller_id": "Product Name", ...}
    """

    # Define the expected files and their sellers
    sellers = ['chef', 'sham', 'sysco', 'usfood', 'greco', 'depot', 'perf']

    # Dictionary to track all product relationships
    # Key: product_id, Value: set of all related product_ids
    product_relationships = defaultdict(set)

    # Dictionary to store product names
    # Key: product_id, Value: product_name
    product_names = {}

    print("Loading match files...")

    for seller in sellers:
        filepath = os.path.join(matches_dir, f"{seller}.json")
        data = load_json_file(filepath)

        if not data:
            continue

        print(f"Processing {filepath} - {len(data)} entries")

        product_key = get_product_key_name(seller)

        for entry in data:
            # Get the main product for this seller
            main_product = entry.get(product_key, {})
            if not main_product:
                continue

            # Should have exactly one key-value pair
            if len(main_product) != 1:
                print(f"Warning: Expected 1 product in {product_key}, got {len(main_product)}")
                continue

            main_product_id = list(main_product.keys())[0]
            main_product_name = list(main_product.values())[0]

            # Store the product name
            product_names[main_product_id] = main_product_name

            # Get all matches for this product
            matches = entry.get('matches', {})

            # Handle case where matches might be a list or other type
            if not isinstance(matches, dict):
                print(f"Warning: matches is not a dict for {main_product_id}, type: {type(matches)}")
                matches = {}

            # Create a set of all related products (including the main product)
            related_products = {main_product_id}
            for match_id, match_name in matches.items():
                related_products.add(match_id)
                product_names[match_id] = match_name

            # Update relationships - each product is related to all others in this group
            for product_id in related_products:
                product_relationships[product_id].update(related_products)

    print(f"Found {len(product_names)} unique products")
    print(f"Building unified match groups...")

    # Build unified match groups using Union-Find approach
    visited = set()
    unified_matches = []

    for product_id in product_names:
        if product_id in visited:
            continue

        # Find all products in this connected component
        match_group = {}
        to_visit = [product_id]
        group_visited = set()

        while to_visit:
            current_id = to_visit.pop()
            if current_id in group_visited:
                continue

            group_visited.add(current_id)
            visited.add(current_id)

            # Add this product to the match group
            if current_id in product_names:
                match_group[current_id] = product_names[current_id]

            # Add all related products to visit
            for related_id in product_relationships[current_id]:
                if related_id not in group_visited:
                    to_visit.append(related_id)

        # Only add groups with more than one product (actual matches)
        if len(match_group) > 1:
            unified_matches.append(match_group)
        elif len(match_group) == 1:
            # Single products with no matches - still include them
            unified_matches.append(match_group)

    print(f"Created {len(unified_matches)} unified match groups")

    # Sort by number of products in each group (largest first)
    unified_matches.sort(key=len, reverse=True)

    return unified_matches

def save_unified_matches(unified_matches: List[Dict[str, str]], output_file: str = "unified_matches.json"):
    """Save the unified matches to a JSON file."""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(unified_matches, f, indent=2, ensure_ascii=False)
        print(f"Saved {len(unified_matches)} match groups to {output_file}")
    except Exception as e:
        print(f"Error saving to {output_file}: {e}")

def main():
    """Main function to run the consolidation."""
    print("=== Product Match Consolidation ===")

    # Check if matches directory exists
    if not os.path.exists("matches"):
        print("Error: 'matches' directory not found")
        return

    # Consolidate matches
    unified_matches = consolidate_matches()

    if not unified_matches:
        print("No matches found to consolidate")
        return

    # Save results
    save_unified_matches(unified_matches)

    # Print summary statistics
    print("\n=== Summary ===")
    print(f"Total match groups: {len(unified_matches)}")

    # Count groups by size
    size_counts = defaultdict(int)
    for group in unified_matches:
        size_counts[len(group)] += 1

    print("Groups by size:")
    for size in sorted(size_counts.keys(), reverse=True):
        count = size_counts[size]
        print(f"  {size} products: {count} groups")

    # Show largest groups
    print(f"\nLargest match groups:")
    for i, group in enumerate(unified_matches[:5]):
        print(f"  Group {i+1}: {len(group)} products")
        for product_id, name in list(group.items())[:3]:
            print(f"    {product_id}: {name[:60]}...")
        if len(group) > 3:
            print(f"    ... and {len(group) - 3} more")

if __name__ == "__main__":
    main()
