#!/usr/bin/env python3
"""
Task 1: Unify objects in each seller file.
Convert from {sellerProduct: {}, matches: {}} format to single unified objects.
"""

import json
import os
from typing import Dict, List, Any

def load_json_file(filepath: str) -> List[Dict[str, Any]]:
    """Load and parse a JSON file."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: File {filepath} not found")
        return []
    except json.JSONDecodeError as e:
        print(f"Error parsing {filepath}: {e}")
        return []

def save_json_file(data: List[Dict[str, str]], filepath: str) -> None:
    """Save data to a JSON file."""
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        print(f"Saved {len(data)} objects to {filepath}")
    except Exception as e:
        print(f"Error saving to {filepath}: {e}")

def unify_seller_file(seller: str, input_dir: str = "matches", output_dir: str = "unified_objects") -> int:
    """
    Unify objects in a single seller file.
    
    Args:
        seller: Name of the seller (e.g., 'chef', 'sham')
        input_dir: Directory containing original files
        output_dir: Directory to save unified files
        
    Returns:
        Number of unified objects created
    """
    input_filepath = os.path.join(input_dir, f"{seller}.json")
    output_filepath = os.path.join(output_dir, f"{seller}_unified.json")
    
    # Load original data
    data = load_json_file(input_filepath)
    if not data:
        return 0
    
    print(f"\nProcessing {seller}.json - {len(data)} entries")
    
    unified_objects = []
    product_key = f"{seller}Product"
    
    for entry in data:
        # Get the main product
        main_product = entry.get(product_key, {})
        if not main_product or len(main_product) != 1:
            print(f"  Warning: Invalid main product structure in entry")
            continue
            
        # Get matches
        matches = entry.get('matches', {})
        
        # Handle case where matches might be a list or other type
        if not isinstance(matches, dict):
            print(f"  Warning: matches is not a dict, type: {type(matches)}")
            matches = {}
        
        # Create unified object by merging main product and matches
        unified_object = {}
        unified_object.update(main_product)  # Add the main product
        unified_object.update(matches)       # Add all matches
        
        # Only add if we have at least one product
        if unified_object:
            unified_objects.append(unified_object)
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Save unified objects
    save_json_file(unified_objects, output_filepath)
    
    print(f"  Original entries: {len(data)}")
    print(f"  Unified objects: {len(unified_objects)}")
    
    return len(unified_objects)

def main():
    """Main function to unify all seller files."""
    print("=== Task 1: Unifying Objects in Each Seller File ===")
    
    # Check if matches directory exists
    if not os.path.exists("matches"):
        print("Error: 'matches' directory not found")
        return
    
    # Define sellers
    sellers = ['chef', 'sham', 'sysco', 'usfood', 'greco', 'depot', 'perf']
    
    total_objects = 0
    results = {}
    
    for seller in sellers:
        count = unify_seller_file(seller)
        results[seller] = count
        total_objects += count
    
    # Print summary
    print(f"\n=== Task 1 Summary ===")
    print(f"Total unified objects across all sellers: {total_objects}")
    print("\nObjects per seller:")
    for seller, count in results.items():
        print(f"  {seller}: {count} objects")
    
    print(f"\nUnified files saved in 'unified_objects/' directory")

if __name__ == "__main__":
    main()
