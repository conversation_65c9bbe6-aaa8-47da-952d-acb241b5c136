#!/usr/bin/env python3
"""
Task 2: Remove objects with identical key-value pairs.
First remove duplicates within each seller file, then merge all and remove cross-seller duplicates.
"""

import json
import os
from typing import Dict, List, Set, Tuple
from collections import defaultdict

def load_json_file(filepath: str) -> List[Dict[str, str]]:
    """Load and parse a JSON file."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: File {filepath} not found")
        return []
    except json.JSONDecodeError as e:
        print(f"Error parsing {filepath}: {e}")
        return []

def save_json_file(data: List[Dict[str, str]], filepath: str) -> None:
    """Save data to a JSON file."""
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        print(f"Saved {len(data)} objects to {filepath}")
    except Exception as e:
        print(f"Error saving to {filepath}: {e}")

def object_to_signature(obj: Dict[str, str]) -> str:
    """
    Convert an object to a signature for comparison.
    Returns a string representation of sorted (key, value) pairs.
    """
    # Convert to string to handle any non-hashable values
    sorted_items = sorted(obj.items())
    return str(sorted_items)

def remove_duplicates_within_seller(seller: str, input_dir: str = "unified_objects",
                                  output_dir: str = "deduplicated_objects") -> Tuple[int, int]:
    """
    Remove duplicate objects within a single seller file.

    Args:
        seller: Name of the seller
        input_dir: Directory containing unified files
        output_dir: Directory to save deduplicated files

    Returns:
        Tuple of (original_count, final_count)
    """
    input_filepath = os.path.join(input_dir, f"{seller}_unified.json")
    output_filepath = os.path.join(output_dir, f"{seller}_deduplicated.json")

    # Load unified data
    data = load_json_file(input_filepath)
    if not data:
        return 0, 0

    print(f"\nProcessing {seller}_unified.json - {len(data)} objects")

    # Track unique objects by their signature
    seen_signatures = set()
    unique_objects = []
    duplicates_removed = 0

    for obj in data:
        signature = object_to_signature(obj)

        if signature not in seen_signatures:
            seen_signatures.add(signature)
            unique_objects.append(obj)
        else:
            duplicates_removed += 1

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Save deduplicated objects
    save_json_file(unique_objects, output_filepath)

    print(f"  Original objects: {len(data)}")
    print(f"  Unique objects: {len(unique_objects)}")
    print(f"  Duplicates removed: {duplicates_removed}")

    return len(data), len(unique_objects)

def merge_and_deduplicate_all(input_dir: str = "deduplicated_objects",
                             output_file: str = "final_unified_matches.json") -> Tuple[int, int, Dict[str, int]]:
    """
    Merge all seller files and remove cross-seller duplicates.

    Args:
        input_dir: Directory containing deduplicated seller files
        output_file: Final output file

    Returns:
        Tuple of (total_input_objects, final_unique_objects, objects_per_seller_in_final)
    """
    sellers = ['chef', 'sham', 'sysco', 'usfood', 'greco', 'depot', 'perf']

    print(f"\n=== Merging All Sellers and Removing Cross-Seller Duplicates ===")

    all_objects = []
    objects_per_seller = {}

    # Load all deduplicated files
    for seller in sellers:
        filepath = os.path.join(input_dir, f"{seller}_deduplicated.json")
        data = load_json_file(filepath)

        if data:
            print(f"Loaded {len(data)} objects from {seller}")
            all_objects.extend(data)
            objects_per_seller[seller] = len(data)
        else:
            objects_per_seller[seller] = 0

    total_input = len(all_objects)
    print(f"\nTotal objects before cross-seller deduplication: {total_input}")

    # Remove cross-seller duplicates
    seen_signatures = set()
    unique_objects = []
    cross_seller_duplicates = 0

    # Track which sellers contribute to final result
    final_objects_per_seller = defaultdict(int)

    for obj in all_objects:
        signature = object_to_signature(obj)

        if signature not in seen_signatures:
            seen_signatures.add(signature)
            unique_objects.append(obj)

            # Count which seller this object likely came from (first key's prefix)
            if obj:
                first_key = list(obj.keys())[0]
                if '_' in first_key:
                    seller_prefix = first_key.split('_')[0]
                elif '#' in first_key:
                    seller_prefix = first_key.split('#')[0].replace('usfood_', 'usfood')
                else:
                    seller_prefix = 'unknown'
                final_objects_per_seller[seller_prefix] += 1
        else:
            cross_seller_duplicates += 1

    # Save final result
    save_json_file(unique_objects, output_file)

    print(f"Final unique objects: {len(unique_objects)}")
    print(f"Cross-seller duplicates removed: {cross_seller_duplicates}")

    return total_input, len(unique_objects), dict(final_objects_per_seller)

def analyze_final_results(filepath: str = "final_unified_matches.json") -> None:
    """Analyze the final results for review."""
    print(f"\n=== Final Results Analysis ===")

    data = load_json_file(filepath)
    if not data:
        return

    print(f"Total unique match groups: {len(data)}")

    # Analyze group sizes
    size_distribution = defaultdict(int)
    for obj in data:
        size = len(obj)
        size_distribution[size] += 1

    print(f"\nGroup size distribution:")
    for size in sorted(size_distribution.keys(), reverse=True):
        count = size_distribution[size]
        print(f"  {size} products: {count} groups")

    # Show some examples
    print(f"\nExample groups:")

    # Sort by size for examples
    sorted_objects = sorted(data, key=len, reverse=True)

    for i, obj in enumerate(sorted_objects[:5]):
        print(f"\nGroup {i+1} ({len(obj)} products):")
        for j, (product_id, name) in enumerate(obj.items()):
            if j < 3:  # Show first 3
                print(f"  {product_id}: {name[:60]}...")
            elif j == 3 and len(obj) > 3:
                print(f"  ... and {len(obj) - 3} more products")
                break

def main():
    """Main function for Task 2."""
    print("=== Task 2: Removing Duplicate Objects ===")

    # Check if unified_objects directory exists
    if not os.path.exists("unified_objects"):
        print("Error: 'unified_objects' directory not found. Run task1_unify_objects.py first.")
        return

    sellers = ['chef', 'sham', 'sysco', 'usfood', 'greco', 'depot', 'perf']

    # Step 1: Remove duplicates within each seller file
    print("\n--- Step 1: Removing duplicates within each seller file ---")

    total_original = 0
    total_after_dedup = 0
    seller_results = {}

    for seller in sellers:
        original, final = remove_duplicates_within_seller(seller)
        seller_results[seller] = {
            'original': original,
            'final': final,
            'removed': original - final
        }
        total_original += original
        total_after_dedup += final

    # Step 2: Merge all and remove cross-seller duplicates
    print("\n--- Step 2: Merging and removing cross-seller duplicates ---")

    total_input, final_count, final_per_seller = merge_and_deduplicate_all()

    # Print comprehensive summary
    print(f"\n=== Task 2 Complete Summary ===")
    print(f"Total original objects: {total_original}")
    print(f"After within-seller deduplication: {total_after_dedup}")
    print(f"Total duplicates removed within sellers: {total_original - total_after_dedup}")
    print(f"After cross-seller deduplication: {final_count}")
    print(f"Cross-seller duplicates removed: {total_after_dedup - final_count}")
    print(f"Total duplicates removed overall: {total_original - final_count}")

    print(f"\nDuplicates removed per seller (within-seller only):")
    for seller, results in seller_results.items():
        print(f"  {seller}: {results['removed']} removed ({results['original']} -> {results['final']})")

    print(f"\nFinal objects contributing per seller:")
    for seller, count in final_per_seller.items():
        print(f"  {seller}: {count} objects")

    # Analyze final results
    analyze_final_results()

if __name__ == "__main__":
    main()
